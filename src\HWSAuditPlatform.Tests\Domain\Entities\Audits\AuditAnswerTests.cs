using FluentAssertions;
using HWSAuditPlatform.Domain.Entities.Audits;
using HWSAuditPlatform.Domain.Entities.Templates;
using HWSAuditPlatform.Domain.Enums;

namespace HWSAuditPlatform.Tests.Domain.Entities.Audits;

public class AuditAnswerTests
{
    [Fact]
    public void Constructor_ShouldSetDefaultValues()
    {
        // Act
        var answer = new AuditAnswer();

        // Assert
        answer.AuditId.Should().Be(string.Empty);
        answer.IsNotApplicable.Should().BeFalse();
        answer.AnsweredAt.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(1));
        answer.SelectedOptions.Should().NotBeNull().And.BeEmpty();
        answer.FailureReasons.Should().NotBeNull().And.BeEmpty();
        answer.Attachments.Should().NotBeNull().And.BeEmpty();
    }

    [Fact]
    public void HasAttachments_WithAttachments_ShouldReturnTrue()
    {
        // Arrange
        var answer = new AuditAnswer();
        answer.Attachments.Add(new AuditAttachment());

        // Act & Assert
        answer.HasAttachments.Should().BeTrue();
    }

    [Fact]
    public void HasAttachments_WithoutAttachments_ShouldReturnFalse()
    {
        // Arrange
        var answer = new AuditAnswer();

        // Act & Assert
        answer.HasAttachments.Should().BeFalse();
    }

    [Fact]
    public void HasFailureReasons_WithFailureReasons_ShouldReturnTrue()
    {
        // Arrange
        var answer = new AuditAnswer();
        answer.FailureReasons.Add(new AuditAnswerFailureReason());

        // Act & Assert
        answer.HasFailureReasons.Should().BeTrue();
    }

    [Fact]
    public void HasFailureReasons_WithoutFailureReasons_ShouldReturnFalse()
    {
        // Arrange
        var answer = new AuditAnswer();

        // Act & Assert
        answer.HasFailureReasons.Should().BeFalse();
    }

    [Fact]
    public void GetDisplayValue_WithNotApplicable_ShouldReturnNA()
    {
        // Arrange
        var answer = new AuditAnswer
        {
            IsNotApplicable = true,
            Question = new Question { QuestionType = QuestionType.YesNo }
        };

        // Act & Assert
        answer.GetDisplayValue().Should().Be("N/A");
    }

    [Fact]
    public void GetDisplayValue_WithYesNoQuestion_ShouldReturnYes()
    {
        // Arrange
        var answer = new AuditAnswer
        {
            AnswerBoolean = true,
            Question = new Question { QuestionType = QuestionType.YesNo }
        };

        // Act & Assert
        answer.GetDisplayValue().Should().Be("Yes");
    }

    [Fact]
    public void GetDisplayValue_WithYesNoQuestionFalse_ShouldReturnNo()
    {
        // Arrange
        var answer = new AuditAnswer
        {
            AnswerBoolean = false,
            Question = new Question { QuestionType = QuestionType.YesNo }
        };

        // Act & Assert
        answer.GetDisplayValue().Should().Be("No");
    }

    [Fact]
    public void GetDisplayValue_WithNumericQuestion_ShouldReturnNumericValue()
    {
        // Arrange
        var answer = new AuditAnswer
        {
            AnswerNumeric = 42.5m,
            Question = new Question { QuestionType = QuestionType.Numeric }
        };

        // Act & Assert
        answer.GetDisplayValue().Should().Be("42.5");
    }

    [Fact]
    public void GetDisplayValue_WithDateQuestion_ShouldReturnFormattedDate()
    {
        // Arrange
        var testDate = new DateTime(2023, 12, 25);
        var answer = new AuditAnswer
        {
            AnswerDate = testDate,
            Question = new Question { QuestionType = QuestionType.Date }
        };

        // Act & Assert
        answer.GetDisplayValue().Should().Be("2023-12-25");
    }

    [Fact]
    public void GetDisplayValue_WithSingleSelectQuestion_ShouldReturnOptionText()
    {
        // Arrange
        var option = new QuestionOption { OptionText = "Excellent" };
        var answer = new AuditAnswer
        {
            SelectedOption = option,
            Question = new Question { QuestionType = QuestionType.SingleSelect }
        };

        // Act & Assert
        answer.GetDisplayValue().Should().Be("Excellent");
    }

    [Fact]
    public void GetDisplayValue_WithMultiSelectQuestion_ShouldReturnConcatenatedOptions()
    {
        // Arrange
        var option1 = new QuestionOption { OptionText = "Option A" };
        var option2 = new QuestionOption { OptionText = "Option B" };
        
        var answer = new AuditAnswer
        {
            Question = new Question { QuestionType = QuestionType.MultiSelect }
        };
        
        answer.SelectedOptions.Add(new AuditAnswerSelectedOption { QuestionOption = option1 });
        answer.SelectedOptions.Add(new AuditAnswerSelectedOption { QuestionOption = option2 });

        // Act & Assert
        answer.GetDisplayValue().Should().Be("Option A, Option B");
    }

    [Fact]
    public void GetDisplayValue_WithShortTextQuestion_ShouldReturnTextValue()
    {
        // Arrange
        var answer = new AuditAnswer
        {
            AnswerText = "Short answer",
            Question = new Question { QuestionType = QuestionType.ShortText }
        };

        // Act & Assert
        answer.GetDisplayValue().Should().Be("Short answer");
    }

    [Fact]
    public void GetDisplayValue_WithLongTextQuestion_ShouldReturnTextValue()
    {
        // Arrange
        var answer = new AuditAnswer
        {
            AnswerText = "This is a longer answer with more details.",
            Question = new Question { QuestionType = QuestionType.LongText }
        };

        // Act & Assert
        answer.GetDisplayValue().Should().Be("This is a longer answer with more details.");
    }

    [Fact]
    public void GetDisplayValue_WithNullValues_ShouldReturnEmpty()
    {
        // Arrange
        var answer = new AuditAnswer
        {
            Question = new Question { QuestionType = QuestionType.ShortText }
        };

        // Act & Assert
        answer.GetDisplayValue().Should().BeEmpty();
    }

    [Fact]
    public void AuditAnswer_WithValidData_ShouldSetPropertiesCorrectly()
    {
        // Arrange
        const string auditId = "clh7ckb0x0001qh08w5t6h5zy";
        const int questionId = 1;
        const string comments = "Additional comments";
        var answeredAt = DateTime.UtcNow;

        // Act
        var answer = new AuditAnswer
        {
            AuditId = auditId,
            QuestionId = questionId,
            AnswerBoolean = true,
            Comments = comments,
            AnsweredAt = answeredAt
        };

        // Assert
        answer.AuditId.Should().Be(auditId);
        answer.QuestionId.Should().Be(questionId);
        answer.AnswerBoolean.Should().BeTrue();
        answer.Comments.Should().Be(comments);
        answer.AnsweredAt.Should().Be(answeredAt);
    }

    [Fact]
    public void AuditAnswer_InheritsFromAuditableEntity()
    {
        // Arrange & Act
        var answer = new AuditAnswer();

        // Assert
        answer.RecordVersion.Should().Be(1);
        answer.CreatedAt.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(1));
        answer.UpdatedAt.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(1));
    }

    [Fact]
    public void IsNotApplicable_CanBeSetToTrue()
    {
        // Arrange
        var answer = new AuditAnswer();

        // Act
        answer.IsNotApplicable = true;

        // Assert
        answer.IsNotApplicable.Should().BeTrue();
    }

    [Fact]
    public void Comments_CanBeSetAndRetrieved()
    {
        // Arrange
        var answer = new AuditAnswer();
        const string comments = "This requires follow-up action.";

        // Act
        answer.Comments = comments;

        // Assert
        answer.Comments.Should().Be(comments);
    }
}
